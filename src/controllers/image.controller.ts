import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {inject, service} from '@loopback/core';
import {
	Count,
	CountSchema,
	Filter,
	repository,
	Where,
} from '@loopback/repository';
import {basicAuthorization, RaleonUserService} from '../services';
import {KlaviyoService} from '../services/integrations/klaviyo.service';
import {OpenAiService} from '../services/open-ai.service';
import {api, get, getModelSchemaRef, HttpErrors, param, post, put, Request, requestBody, Response, RestBindings} from '@loopback/rest';
import {GuardMultiHopPropertyStrategy, injectUserOrgId, GuardSkipStrategy, guardStrategy, modelIdForGuard, OrgGuardSingleHopPropertyStrategy, skipGuardCheck} from '../interceptors';
import {uuid} from 'uuidv4';
import {CampaignRepository, ImageRepository} from '../repositories';
const multer = require('multer')
//import * as multer from 'multer';
const upload = multer()
const AWS = require('aws-sdk');

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class ImageController {
	constructor(
		@repository('ImageRepository') private imageRepository: ImageRepository,
		@repository('CampaignRepository') private campaignRepository: CampaignRepository,
		@service(KlaviyoService) private klaviyoService: KlaviyoService,
		@service(OpenAiService) private openAiService: OpenAiService
	) {

	}

	async uploadImageToBucket(filename: string, imageBuffer: Buffer) {
		if (imageBuffer.length > 30 * 1024 * 1024) {
			throw new HttpErrors.PayloadTooLarge('Image size too large');
		}

		const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
		const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
		const s3 = new AWS.S3({
			accessKeyId: AWS_ACCESS_KEY,
			secretAccessKey: AWS_SECRET_KEY,
			region: 'us-east-1'
		});
		try {
			await s3.putObject({
				Bucket: 'raleon-images-cdn',
				Key: filename,
				Body: imageBuffer,
				ContentType: 'image/png',
				ACL : 'public-read'
			}).promise();
		} catch (err) {
			console.error(err);
		}
	}

	getImageURL(filename: string) {
		const s3Domain = 'd3q4ufbgs1i4ak.cloudfront.net';
		const s3Path = '/' + encodeURIComponent(filename);

		return `https://${s3Domain}${s3Path}`;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/images/campaign/{campaignid}/upload', {
		responses: {
			'200': {
				description: 'URI of uploaded image',
				content: {
					'text/plain': {},
				},
			},
		},
	})
	async uploadStandardImage(
		@param.path.number('campaignid') campaignId: number,
		@injectUserOrgId() orgId: number,
		@requestBody.file()
		@inject(RestBindings.Http.RESPONSE) response: Response,
		@inject(RestBindings.Http.REQUEST) request: Request,
	): Promise<string> {
		const filename = `${uuid()}.png`;
		const storage = multer.memoryStorage();
		const upload = multer({storage});

		return new Promise<string>((resolve, reject) => {
			try {
				upload.single('file')(request, response, async (err: any) => {
					try {
						if (err) return reject(err);

						const image = (request as any).file.buffer;

						await this.uploadImageToBucket(filename, image);

						let publicURL = this.getImageURL(filename);

						if (campaignId) {
							await this.imageRepository.deleteAll({
									orgId,
									campaignId,
									friendlyname: "Campaign Image"
							});
							await this.campaignRepository.image(campaignId).create({
								friendlyname: 'Campaign Image',
								url: publicURL,
								orgId: orgId
							});
						}
						else {
							await this.imageRepository.deleteAll({
									orgId,
									campaignId: null as any
							});
							await this.imageRepository.create({
								friendlyname: 'Campaign Image',
								url: publicURL,
								orgId
							});
						}

						resolve(publicURL);
					} catch (e2) {
						console.error(e2);
						reject(e2);
					}
				});
			} catch (e) {
				console.error(e);
				reject(e);
			}
		});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin','customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/images/loyalty-image-gallery/org-images', {
		responses: {
			'200': {
				description: 'URIs of all uploaded images',
				content: {
					'text/plain': {},
				},
			},
		},
	})
	async getOrgImages(
		@injectUserOrgId() orgId: number,
	): Promise<Array<string>> {
		const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
		const AWS_SECRET_KEY = process.env.API_SECRET_KEY;
		const s3 = new AWS.S3({
			accessKeyId: AWS_ACCESS_KEY,
			secretAccessKey: AWS_SECRET_KEY,
			region: 'us-east-1'
		});

		const bucketParams = {
			Bucket: 'loyalty-image-gallery',
			Prefix: 'images/organization=' + orgId
		};

		return await new Promise<Array<string>>(resolve => {
			s3.listObjects(bucketParams, function(err: any, data: any) {
				if (err) {
					console.log("Error", err);
				} else {
					// For each object in the bucket, console log the URL
					const results = data.Contents?.map((object: any) =>
						'https://' + bucketParams.Bucket + '.s3.amazonaws.com/' + object.Key
					);
					console.log(`Found images: ${JSON.stringify(results)}`);
					resolve(results);
				}
			})
		});
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin','customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/klaviyo/images', {
		responses: {
			'200': {
				description: 'Get images from Klaviyo',
				content: {'application/json': {schema: {type: 'object'}}},
			},
		},
	})
	async getKlaviyoImages(
		@param.query.number('pageSize') pageSize: number | 100,
		@param.query.string('pageCursor') pageCursor: string | '',
		@param.query.string('nameFilter') nameFilter: string | '',
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			const params = {
				pageSize: pageSize,
				pageCursor: pageCursor,
				nameFilter: nameFilter
			};

			const result = await this.klaviyoService.getImages(orgId, params);
			return result;
		} catch (error) {
			console.error('Error retrieving Klaviyo images:', error);
			throw new HttpErrors.InternalServerError('Failed to retrieve images from Klaviyo');
		}
	}

	@authenticate('jwt')
	@get('/klaviyo/proxy-image', {
		responses: {
			'200': {
				description: 'Proxy image from external URL to bypass CORS',
				content: {'image/*': {}},
			},
		},
	})
	@skipGuardCheck()
	async proxyKlaviyoImage(
		@param.query.string('url') imageUrl: string,
		@injectUserOrgId() orgId: number,
		@inject(RestBindings.Http.RESPONSE) response: Response,
	): Promise<void> {
		if (!imageUrl) {
			throw new HttpErrors.BadRequest('Image URL is required');
		}

		try {
			console.log(`Proxying image from: ${imageUrl}`);

			// Use Axios to fetch the image
			const axios = require('axios');
			const imageResponse = await axios({
				method: 'get',
				url: imageUrl,
				responseType: 'arraybuffer'
			});

			// Get content type from the response
			const contentType = imageResponse.headers['content-type'] || 'image/jpeg';

			// Set appropriate headers
			response.set('Content-Type', contentType);
			response.set('Access-Control-Allow-Origin', '*');

			// Send the image data
			response.send(imageResponse.data);
		} catch (error) {
			console.error('Error proxying image:', error);
			throw new HttpErrors.InternalServerError('Failed to proxy image');
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/images/detect-text', {
		responses: {
			'200': {
				description: 'Text elements detected in the image',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								textElements: {
									type: 'array',
									items: {
										type: 'object',
										properties: {
											text: { type: 'string' },
											description: { type: 'string' },
											confidence: { type: 'string' }
										}
									}
								}
							}
						}
					}
				},
			},
		},
	})
	async detectTextInImage(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['imageUrl'],
						properties: {
							imageUrl: { type: 'string' }
						}
					}
				}
			}
		})
		requestData: { imageUrl: string },
		@injectUserOrgId() orgId: number
	): Promise<any> {
		try {
			if (!requestData.imageUrl) {
				throw new HttpErrors.BadRequest('Image URL is required');
			}

			// Create a specialized prompt for text extraction
			const prompt = `Analyze this image and identify all text elements visible in it. Return the results in JSON format with the following structure:

{
  "textElements": [
    {
      "text": "exact text found in the image",
      "description": "brief description of the text (e.g., 'main heading', 'button text', 'small caption')",
      "confidence": "high|medium|low based on how clear and readable the text is"
    }
  ]
}

Focus on:
- All readable text, including headings, body text, button text, labels, etc.
- Provide the exact text as it appears (preserve case, punctuation, spacing)
- Include a helpful description of where/what type of text it is
- Rate confidence based on text clarity and readability
- Order by prominence/importance (largest/most important text first)

If no text is found, return: {"textElements": []}`;

			// Call the OpenAI service to analyze the image
			const aiResponse = await this.openAiService.getImageDescription(prompt, requestData.imageUrl);

			// Parse the JSON response
			let parsedResponse;
			try {
				parsedResponse = JSON.parse(aiResponse);
			} catch (parseError) {
				console.error('Error parsing AI response:', parseError);
				throw new HttpErrors.InternalServerError('Failed to parse AI response');
			}

			// Validate the response structure
			if (!parsedResponse.textElements || !Array.isArray(parsedResponse.textElements)) {
				console.error('Invalid AI response structure:', parsedResponse);
				throw new HttpErrors.InternalServerError('Invalid response from text detection service');
			}

			return parsedResponse;

		} catch (error) {
			console.error('Error detecting text in image:', error);
			if (error instanceof HttpErrors.HttpError) {
				throw error;
			}
			throw new HttpErrors.InternalServerError('Failed to detect text in image');
		}
	}
}
