/**
 * Service for wrapping email HTML with interactive image editing capabilities
 */

interface ImageClickEvent {
  type: 'image-clicked';
  imageUrl: string;
  imageAlt: string;
}

export class EmailImageWrapper {
  /**
   * Wraps email HTML with interactive functionality for image editing
   */
  static wrapEmailHtml(originalHtml: string): string {
    if (!originalHtml || originalHtml.trim() === '') {
      return originalHtml;
    }

    const styles = this.extractOriginalStyles(originalHtml);
    const bodyContent = this.extractBodyContent(originalHtml);
    const script = this.generateInteractiveScript();
    const hoverStyles = this.generateHoverStyles();

    return `
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          ${styles}
          <style>
            ${hoverStyles}
          </style>
        </head>
        <body>
          ${bodyContent}
          <script>
            ${script}
          </script>
        </body>
      </html>
    `;
  }

  /**
   * Extract original styles from the HTML
   */
  private static extractOriginalStyles(html: string): string {
    const styleMatches = html.match(/<style[^>]*>([\s\S]*?)<\/style>/gi);
    const linkMatches = html.match(/<link[^>]*rel=['"]stylesheet['"][^>]*>/gi);
    
    let styles = '';
    
    if (styleMatches) {
      styles += styleMatches.join('\n');
    }
    
    if (linkMatches) {
      styles += linkMatches.join('\n');
    }

    // Also check for inline styles in the head
    const headMatch = html.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
    if (headMatch && headMatch[1]) {
      const headContent = headMatch[1];
      const additionalStyles = headContent.match(/<style[^>]*>([\s\S]*?)<\/style>/gi);
      const additionalLinks = headContent.match(/<link[^>]*>/gi);
      
      if (additionalStyles) {
        styles += additionalStyles.join('\n');
      }
      
      if (additionalLinks) {
        styles += additionalLinks.join('\n');
      }
    }

    return styles;
  }

  /**
   * Extract body content from the HTML
   */
  private static extractBodyContent(html: string): string {
    // Try to extract body content first
    const bodyMatch = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
      return bodyMatch[1];
    }

    // If no body tags, try to extract everything between html tags
    const htmlMatch = html.match(/<html[^>]*>([\s\S]*?)<\/html>/i);
    if (htmlMatch && htmlMatch[1]) {
      // Remove head content if present
      const withoutHead = htmlMatch[1].replace(/<head[^>]*>[\s\S]*?<\/head>/i, '');
      return withoutHead;
    }

    // Fallback: return the original HTML (might be just body content)
    return html;
  }

  /**
   * Generate CSS styles for image hover effects
   */
  private static generateHoverStyles(): string {
    return `
      /* Image hover effects for editing */
      img {
        transition: border 0.2s ease, box-shadow 0.2s ease;
      }
      
      img:hover {
        border: 2px solid #6B46C1 !important;
        cursor: pointer !important;
        box-shadow: 0 0 0 2px rgba(107, 70, 193, 0.2) !important;
        border-radius: 4px !important;
      }
      
      /* Ensure images maintain their original styling when not hovered */
      img:not(:hover) {
        transition: border 0.2s ease, box-shadow 0.2s ease;
      }

      /* Handle images within tables/containers */
      table img:hover,
      td img:hover,
      div img:hover {
        border: 2px solid #6B46C1 !important;
        cursor: pointer !important;
        box-shadow: 0 0 0 2px rgba(107, 70, 193, 0.2) !important;
        border-radius: 4px !important;
      }
    `;
  }

  /**
   * Generate JavaScript for interactive functionality
   */
  private static generateInteractiveScript(): string {
    return `
      (function() {
        // Wait for DOM to be fully loaded
        function initImageInteractivity() {
          const images = document.querySelectorAll('img');
          
          images.forEach(function(img) {
            // Remove any existing click listeners to prevent duplicates
            img.removeEventListener('click', handleImageClick);
            
            // Add click listener
            img.addEventListener('click', handleImageClick);
            
            // Add error handling for broken images
            img.addEventListener('error', function() {
              console.warn('Failed to load image:', img.src);
            });
          });
        }
        
        function handleImageClick(event) {
          event.preventDefault();
          event.stopPropagation();
          
          const img = event.target;
          const imageUrl = img.src || img.getAttribute('src') || '';
          const imageAlt = img.alt || img.getAttribute('alt') || '';
          
          // Send message to parent window
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'image-clicked',
              imageUrl: imageUrl,
              imageAlt: imageAlt
            }, '*');
          }
        }
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initImageInteractivity);
        } else {
          initImageInteractivity();
        }
        
        // Re-initialize if new images are added dynamically
        const observer = new MutationObserver(function(mutations) {
          let shouldReinit = false;
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  if (node.tagName === 'IMG' || node.querySelector('img')) {
                    shouldReinit = true;
                  }
                }
              });
            }
          });
          
          if (shouldReinit) {
            setTimeout(initImageInteractivity, 100);
          }
        });
        
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      })();
    `;
  }

  /**
   * Check if HTML contains images
   */
  static hasImages(html: string): boolean {
    const imgRegex = /<img[^>]+>/gi;
    return imgRegex.test(html);
  }

  /**
   * Extract all image URLs from HTML
   */
  static extractImageUrls(html: string): string[] {
    const imgRegex = /<img[^>]+src=['"]([^'"]+)['"][^>]*>/gi;
    const urls: string[] = [];
    let match: RegExpExecArray | null;

    while ((match = imgRegex.exec(html)) !== null) {
      urls.push(match[1]);
    }

    return urls;
  }
}

export type { ImageClickEvent };